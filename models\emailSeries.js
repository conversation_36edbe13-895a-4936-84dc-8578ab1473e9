const sequelize = require('sequelize');
const moment = require('moment');
const con = require('./connection');

// Email Series Schema
var emailSeriesSchema = con.define('ig_email_series', {
    series_name: {
        type: sequelize.STRING,
        allowNull: false
    },
    topic: {
        type: sequelize.STRING,
        allowNull: false
    },
    audience: {
        type: sequelize.STRING,
        allowNull: false
    },
    tone: {
        type: sequelize.STRING,
        defaultValue: 'professional'
    },
    total_emails: {
        type: sequelize.INTEGER,
        allowNull: false
    },
    status: {
        type: sequelize.ENUM('draft', 'active', 'completed', 'paused'),
        defaultValue: 'draft'
    },
    ai_prompt: {
        type: sequelize.TEXT
    },
    user_id: {
        type: sequelize.INTEGER,
        allowNull: false
    },
    createdAt: {
        type: sequelize.DATE,
        get: function () {
            const rawValue = this.getDataValue('createdAt');
            if (rawValue) {
                const isoDate = moment(rawValue, moment.ISO_8601);
                return isoDate.isValid() ? isoDate.format('YYYY-MM-DD HH:mm:ss') : false;
            }
            return false;
        }
    },
    updatedAt: {
        type: sequelize.DATE,
        get: function () {
            const rawValue = this.getDataValue('updatedAt');
            if (rawValue) {
                const isoDate = moment(rawValue, moment.ISO_8601);
                return isoDate.isValid() ? isoDate.format('YYYY-MM-DD HH:mm:ss') : false;
            }
            return false;
        }
    }
});

// Remove immediate sync - will be handled centrally

module.exports = emailSeriesSchema;
