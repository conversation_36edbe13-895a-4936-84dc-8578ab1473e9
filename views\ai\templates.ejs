<%- include('../header', {menu:'ai'}) %>

<div class="ig_content_wrapper">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="ig_page_header">
                    <h2>AI Templates</h2>
                    <p><PERSON><PERSON><PERSON> and manage AI-generated email templates</p>
                    <div class="ig_page_actions">
                        <a href="/ai/create-template" class="ig_btn ig_btn_primary">
                            <i class="fas fa-plus"></i> Create New Template
                        </a>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="ig_filter_wrapper">
                    <form method="GET" action="/ai/templates">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="ig_input_wrapper">
                                    <input type="text" name="search" class="ig_input" placeholder="Search templates..." value="<%= search %>">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="ig_input_wrapper">
                                    <select name="category" class="ig_input ig_select">
                                        <option value="">All Categories</option>
                                        <option value="newsletter" <%= category === 'newsletter' ? 'selected' : '' %>>Newsletter</option>
                                        <option value="promotional" <%= category === 'promotional' ? 'selected' : '' %>>Promotional</option>
                                        <option value="welcome" <%= category === 'welcome' ? 'selected' : '' %>>Welcome</option>
                                        <option value="follow-up" <%= category === 'follow-up' ? 'selected' : '' %>>Follow-up</option>
                                        <option value="general" <%= category === 'general' ? 'selected' : '' %>>General</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="ig_btn ig_btn_primary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <% if(search || category) { %>
                                    <a href="/ai/templates" class="ig_btn ig_btn_secondary">Clear</a>
                                <% } %>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Templates Grid -->
                <div class="ig_templates_grid">
                    <% if(templates && templates.length > 0) { %>
                        <div class="row">
                            <% templates.forEach(function(template) { %>
                                <div class="col-md-6 col-lg-4">
                                    <div class="ig_template_card">
                                        <div class="ig_template_header">
                                            <h4><%= template.template_name %></h4>
                                            <span class="ig_template_type"><%= template.template_type %></span>
                                        </div>
                                        <div class="ig_template_body">
                                            <p class="ig_template_description">
                                                <%= template.description || 'No description available' %>
                                            </p>
                                            <div class="ig_template_meta">
                                                <span class="ig_template_style">Style: <%= template.style %></span>
                                                <span class="ig_template_category">Category: <%= template.category %></span>
                                            </div>
                                        </div>
                                        <div class="ig_template_footer">
                                            <div class="ig_template_actions">
                                                <button class="ig_btn ig_btn_small ig_btn_primary preview-template" data-id="<%= template.id %>">
                                                    <i class="fas fa-eye"></i> Preview
                                                </button>
                                                <button class="ig_btn ig_btn_small ig_btn_secondary use-template" data-id="<%= template.id %>">
                                                    <i class="fas fa-check"></i> Use
                                                </button>
                                                <% if(template.user_id == locals.userId) { %>
                                                    <button class="ig_btn ig_btn_small ig_btn_danger delete-template" data-id="<%= template.id %>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <% } %>
                                            </div>
                                            <div class="ig_template_stats">
                                                <small>Used <%= template.usage_count %> times</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <% }); %>
                        </div>

                        <!-- Pagination -->
                        <% if(pagination.totalPages > 1) { %>
                            <div class="ig_pagination">
                                <% if(pagination.hasPrevPage) { %>
                                    <a href="/ai/templates?page=<%= pagination.currentPage - 1 %>&search=<%= search %>&category=<%= category %>" class="ig_btn ig_btn_secondary">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </a>
                                <% } %>
                                
                                <span class="ig_page_info">
                                    Page <%= pagination.currentPage %> of <%= pagination.totalPages %>
                                </span>
                                
                                <% if(pagination.hasNextPage) { %>
                                    <a href="/ai/templates?page=<%= pagination.currentPage + 1 %>&search=<%= search %>&category=<%= category %>" class="ig_btn ig_btn_secondary">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </a>
                                <% } %>
                            </div>
                        <% } %>
                    <% } else { %>
                        <div class="ig_empty_state">
                            <div class="ig_empty_icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <h3>No Templates Found</h3>
                            <p>Create your first AI-generated email template to get started.</p>
                            <a href="/ai/create-template" class="ig_btn ig_btn_primary">
                                <i class="fas fa-plus"></i> Create Template
                            </a>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template Preview Modal -->
<div class="modal fade" id="templatePreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Template Preview</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="templatePreviewContent">
                    <!-- Template preview will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="ig_btn ig_btn_secondary" data-dismiss="modal">Close</button>
                <button type="button" class="ig_btn ig_btn_primary" id="useTemplateBtn">Use Template</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let currentTemplateId = null;

    // Preview template
    $('.preview-template').click(function() {
        const templateId = $(this).data('id');
        currentTemplateId = templateId;
        
        $.ajax({
            url: `/ai/templates/${templateId}`,
            method: 'GET',
            success: function(response) {
                if (response.status) {
                    $('#templatePreviewContent').html(response.template.content);
                    $('#templatePreviewModal').modal('show');
                } else {
                    alert('Error loading template: ' + response.message);
                }
            },
            error: function() {
                alert('Error loading template');
            }
        });
    });

    // Use template
    $('.use-template, #useTemplateBtn').click(function() {
        const templateId = $(this).data('id') || currentTemplateId;
        
        if (confirm('Use this template for a new campaign?')) {
            window.location.href = `/campaigns/create?template_id=${templateId}`;
        }
    });

    // Delete template
    $('.delete-template').click(function() {
        const templateId = $(this).data('id');
        
        if (confirm('Are you sure you want to delete this template?')) {
            $.ajax({
                url: `/ai/templates/${templateId}`,
                method: 'DELETE',
                success: function(response) {
                    if (response.status) {
                        location.reload();
                    } else {
                        alert('Error deleting template: ' + response.message);
                    }
                },
                error: function() {
                    alert('Error deleting template');
                }
            });
        }
    });
});
</script>

<%- include('../footer') %>
