const express = require('express');
const router = express.Router();
const aiController = require('../controller/ai');

// Email Series Routes
router.get('/email-series', aiController.getEmailSeries);
router.get('/create-series', aiController.showCreateSeries);
router.post('/generate-series', aiController.generateEmailSeries);
router.get('/series/:id', aiController.getSeriesDetails);

// AI Templates Routes
router.get('/templates', aiController.getAITemplates);
router.get('/create-template', aiController.showCreateTemplate);
router.post('/generate-template', aiController.generateAITemplate);
router.get('/templates/:id', (req, res) => {
    // Get individual template details
    res.json({ status: true, template: { content: 'Template content here' } });
});

// Prompt Generator Routes
router.get('/prompt-generator', aiController.showPromptGenerator);
router.post('/generate-prompts', aiController.generatePromptSuggestions);

module.exports = router;
