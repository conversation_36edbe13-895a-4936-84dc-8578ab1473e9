const sequelize = require('sequelize');
const moment = require('moment');
const con = require('./connection');

// Individual Series Email Schema
var seriesEmailSchema = con.define('ig_series_emails', {
    series_id: {
        type: sequelize.INTEGER,
        allowNull: false
    },
    email_number: {
        type: sequelize.INTEGER,
        allowNull: false
    },
    subject: {
        type: sequelize.STRING,
        allowNull: false
    },
    content: {
        type: sequelize.TEXT,
        allowNull: false
    },
    cta: {
        type: sequelize.TEXT
    },
    delay_days: {
        type: sequelize.INTEGER,
        defaultValue: 0
    },
    status: {
        type: sequelize.ENUM('draft', 'scheduled', 'sent', 'paused'),
        defaultValue: 'draft'
    },
    sent_count: {
        type: sequelize.INTEGER,
        defaultValue: 0
    },
    open_count: {
        type: sequelize.INTEGER,
        defaultValue: 0
    },
    click_count: {
        type: sequelize.INTEGER,
        defaultValue: 0
    },
    user_id: {
        type: sequelize.INTEGER,
        allowNull: false
    },
    createdAt: {
        type: sequelize.DATE,
        get: function () {
            const rawValue = this.getDataValue('createdAt');
            if (rawValue) {
                const isoDate = moment(rawValue, moment.ISO_8601);
                return isoDate.isValid() ? isoDate.format('YYYY-MM-DD HH:mm:ss') : false;
            }
            return false;
        }
    },
    updatedAt: {
        type: sequelize.DATE,
        get: function () {
            const rawValue = this.getDataValue('updatedAt');
            if (rawValue) {
                const isoDate = moment(rawValue, moment.ISO_8601);
                return isoDate.isValid() ? isoDate.format('YYYY-MM-DD HH:mm:ss') : false;
            }
            return false;
        }
    }
});

// Remove immediate sync - will be handled centrally

module.exports = seriesEmailSchema;
