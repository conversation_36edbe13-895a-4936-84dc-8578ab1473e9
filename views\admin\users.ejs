<%- include('../header', {menu:'users', userType: userType}) %>

<div class="ig_content_wrapper">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="ig_breadcrumb_wrapper">
                    <div class="ig_breadcrumb_main">
                        <h1>User Management</h1>
                        <a href="#" class="ig_btn" onclick="showCreateUserModal()">Create New User</a>
                    </div>
                </div>
                <div class="ig_table_wrapper">
                    <table class="table table-hover" id="users_table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Full Name</th>
                                <th>Email</th>
                                <th>User Type</th>
                                <th>Status</th>
                                <th>Created At</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% users.forEach(function(user) { %>
                                <tr>
                                    <td><%= user.id %></td>
                                    <td><%= user.full_name %></td>
                                    <td><%= user.email %></td>
                                    <td>
                                        <span class="badge badge-<%= user.user_type === 'admin' ? 'danger' : user.user_type === 'unlimited' ? 'primary' : user.user_type === 'agency' ? 'warning' : 'secondary' %>">
                                            <%= user.user_type.charAt(0).toUpperCase() + user.user_type.slice(1) %>
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge badge-<%= user.status == 1 ? 'success' : 'secondary' %>">
                                            <%= user.status == 1 ? 'Active' : 'Inactive' %>
                                        </span>
                                    </td>
                                    <td><%= user.createdAt || 'N/A' %></td>
                                    <td>
                                        <button class="btn btn-sm btn-warning" onclick="editUser(<%= user.id %>)"><i class="fa fa-edit"></i></button>
                                        <button class="btn btn-sm btn-danger" onclick="deleteUser(<%= user.id %>)"><i class="fa fa-trash"></i></button>
                                    </td>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit User</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="edit_user_id">
                    <div class="form-group">
                        <label>Full Name</label>
                        <input type="text" class="form-control" id="edit_full_name" required>
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" class="form-control" id="edit_email" required>
                    </div>
                    <div class="form-group">
                        <label>New Password (leave blank to keep current)</label>
                        <input type="password" class="form-control" id="edit_password" placeholder="Leave blank to keep current password">
                    </div>
                    <div class="form-group">
                        <label>User Type</label>
                        <select class="form-control" id="edit_user_type">
                            <option value="basic">Basic</option>
                            <option value="unlimited">Unlimited</option>
                            <option value="agency">Agency</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Status</label>
                        <select class="form-control" id="edit_status">
                            <option value="1">Active</option>
                            <option value="0">Inactive</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateUser()">Update User</button>
            </div>
        </div>
    </div>
</div>

<!-- Create User Modal -->
<div class="modal fade" id="createUserModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New User</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="createUserForm">
                    <div class="form-group">
                        <label>Full Name</label>
                        <input type="text" class="form-control" id="create_full_name" required>
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" class="form-control" id="create_email" required>
                    </div>
                    <div class="form-group">
                        <label>Password</label>
                        <input type="password" class="form-control" id="create_password" required>
                    </div>
                    <div class="form-group">
                        <label>User Type</label>
                        <select class="form-control" id="create_user_type">
                            <option value="basic">Basic</option>
                            <option value="unlimited">Unlimited</option>
                            <option value="agency">Agency</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createUser()">Create User</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#users_table').DataTable();
});

function showCreateUserModal() {
    $('#createUserModal').modal('show');
}

function createUser() {
    // Implementation for creating user
    var data = {
        full_name: $('#create_full_name').val(),
        email: $('#create_email').val(),
        password: $('#create_password').val(),
        user_type: $('#create_user_type').val()
    };
    
    $.ajax({
        url: '/admin/users/create',
        method: 'POST',
        data: data,
        success: function(response) {
            if(response.status) {
                alert('User created successfully!');
                location.reload();
            } else {
                alert('Error: ' + response.message);
            }
        }
    });
}

function editUser(id) {
    // Fetch user data and populate the edit form
    $.ajax({
        url: '/admin/users/get/' + id,
        method: 'GET',
        success: function(response) {
            if(response.status) {
                $('#edit_user_id').val(response.user.id);
                $('#edit_full_name').val(response.user.full_name);
                $('#edit_email').val(response.user.email);
                $('#edit_user_type').val(response.user.user_type);
                $('#edit_status').val(response.user.status);
                $('#edit_password').val(''); // Clear password field
                $('#editUserModal').modal('show');
            } else {
                alert('Error: ' + response.message);
            }
        },
        error: function() {
            alert('Error fetching user data');
        }
    });
}

function updateUser() {
    var userId = $('#edit_user_id').val();
    var data = {
        full_name: $('#edit_full_name').val(),
        email: $('#edit_email').val(),
        password: $('#edit_password').val(),
        user_type: $('#edit_user_type').val(),
        status: $('#edit_status').val()
    };
    
    $.ajax({
        url: '/admin/users/update/' + userId,
        method: 'PUT',
        data: data,
        success: function(response) {
            if(response.status) {
                alert('User updated successfully!');
                $('#editUserModal').modal('hide');
                location.reload();
            } else {
                alert('Error: ' + response.message);
            }
        },
        error: function() {
            alert('Error updating user');
        }
    });
}

function deleteUser(id) {
    if(confirm('Are you sure you want to delete this user?')) {
        $.ajax({
            url: '/admin/users/delete/' + id,
            method: 'DELETE',
            success: function(response) {
                if(response.status) {
                    alert('User deleted successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + response.message);
                }
            }
        });
    }
}
</script>

<%- include('../footer') %>
