InboxGun - nodejs email builder app 

What Do You need for use??

1. User has installation of Node-Js 

2. install npm module
      -> npm install
      
3. User must create database and update /config/db.js file with database details.

4. User can change hostname and port from /bin/www file.

5. For use this InboxGun, User have to register first.

6. For forgot-password, we use SMTP and user must update /config/mail_setting.json file with SMTP details.

7. For sending template, User have to fill smtp details from settings.