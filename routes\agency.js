const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');

// Models
const User = require('../models/users');

// Middleware to ensure the user is an agency
function isAgency(req, res, next) {
    if (req.session.userType === 'agency') {
        return next();
    }
    res.redirect('/');
}

// Agency User Management Dashboard
router.get('/agency/users', isAgency, (req, res) => {
    const agencyId = req.session.Id;
    
    User.findAll({ 
        where: { 
            created_by_agency: agencyId,
            user_type: 'basic'
        },
        include: [{
            model: User,
            as: 'Agency',
            attributes: ['id', 'full_name', 'email']
        }],
        order: [['id', 'DESC']] 
    })
    .then(users => {
        res.render('agency/users', { 
            users, 
            userType: req.session.userType,
            agencyName: req.session.userName,
            menu: 'agency-users'
        });
    })
    .catch(err => {
        console.error('Error fetching agency users:', err);
        res.status(500).json({ status: false, message: 'Could not load users.' });
    });
});

// Create new basic user by agency
router.post('/agency/users/create', isAgency, (req, res) => {
    const { email, full_name, password } = req.body;
    const agencyId = req.session.Id;
    
    // Validate input
    if (!email || !full_name || !password) {
        return res.json({ status: false, message: 'All fields are required!' });
    }
    
    User.findOne({ where: { email: email } })
        .then(existingUser => {
            if (existingUser) {
                return res.json({ status: false, message: 'Email already exists!' });
            }
            
            bcrypt.hash(password, 8, (err, hash) => {
                if (err) {
                    return res.json({ status: false, message: 'Error hashing password!' });
                }
                
                const newUser = {
                    email: email,
                    full_name: full_name,
                    password: hash,
                    user_type: 'basic',
                    created_by_agency: agencyId,
                    status: 1
                };
                
                User.create(newUser)
                    .then(user => {
                        res.json({ status: true, message: 'Basic user created successfully!' });
                    })
                    .catch(err => {
                        console.error('Error creating user:', err);
                        res.json({ status: false, message: 'Error creating user!' });
                    });
            });
        })
        .catch(err => {
            console.error('Error checking email:', err);
            res.json({ status: false, message: 'Error checking email!' });
        });
});

// Get single basic user managed by agency
router.get('/agency/users/get/:id', isAgency, (req, res) => {
    const userId = req.params.id;
    const agencyId = req.session.Id;
    
    User.findOne({ 
        where: { 
            id: userId, 
            created_by_agency: agencyId,
            user_type: 'basic'
        } 
    })
    .then(user => {
        if (user) {
            res.json({ status: true, user: user });
        } else {
            res.json({ status: false, message: 'User not found or not authorized!' });
        }
    })
    .catch(err => {
        console.error('Error fetching user:', err);
        res.json({ status: false, message: 'Error fetching user!' });
    });
});

// Update basic user by agency
router.put('/agency/users/update/:id', isAgency, (req, res) => {
    const userId = req.params.id;
    const agencyId = req.session.Id;
    const { email, full_name, password, status } = req.body;
    
    // Validate input
    if (!email || !full_name) {
        return res.json({ status: false, message: 'Email and Full Name are required!' });
    }
    
    // First check if user exists and belongs to this agency
    User.findOne({ 
        where: { 
            id: userId, 
            created_by_agency: agencyId,
            user_type: 'basic'
        } 
    })
    .then(user => {
        if (!user) {
            return res.json({ status: false, message: 'User not found or not authorized!' });
        }
        
        // Check if email is being changed and if it's already taken
        if (email !== user.email) {
            User.findOne({ where: { email: email } })
                .then(existingUser => {
                    if (existingUser) {
                        return res.json({ status: false, message: 'Email already exists!' });
                    }
                    
                    // Proceed with update
                    updateUserData();
                })
                .catch(err => {
                    console.error('Error checking email:', err);
                    res.json({ status: false, message: 'Error checking email!' });
                });
        } else {
            // Email not changed, proceed with update
            updateUserData();
        }
        
        function updateUserData() {
            const updateData = {
                email: email,
                full_name: full_name,
                status: status || 1
            };
            
            // Only update password if provided
            if (password && password.trim() !== '') {
                bcrypt.hash(password, 8, (err, hash) => {
                    if (err) {
                        return res.json({ status: false, message: 'Error hashing password!' });
                    }
                    
                    updateData.password = hash;
                    updateData.resetPasswordToken = hash;
                    
                    performUpdate();
                });
            } else {
                performUpdate();
            }
            
            function performUpdate() {
                User.update(updateData, { 
                    where: { 
                        id: userId, 
                        created_by_agency: agencyId,
                        user_type: 'basic'
                    } 
                })
                .then(result => {
                    if (result[0] > 0) {
                        res.json({ status: true, message: 'User updated successfully!' });
                    } else {
                        res.json({ status: false, message: 'User not found or not authorized!' });
                    }
                })
                .catch(err => {
                    console.error('Error updating user:', err);
                    res.json({ status: false, message: 'Error updating user!' });
                });
            }
        }
    })
    .catch(err => {
        console.error('Error finding user:', err);
        res.json({ status: false, message: 'Error finding user!' });
    });
});

// Delete basic user by agency
router.delete('/agency/users/delete/:id', isAgency, (req, res) => {
    const userId = req.params.id;
    const agencyId = req.session.Id;
    
    User.destroy({ 
        where: { 
            id: userId, 
            created_by_agency: agencyId,
            user_type: 'basic'
        } 
    })
    .then(result => {
        if (result) {
            res.json({ status: true, message: 'User deleted successfully!' });
        } else {
            res.json({ status: false, message: 'User not found or not authorized!' });
        }
    })
    .catch(err => {
        console.error('Error deleting user:', err);
        res.json({ status: false, message: 'Error deleting user!' });
    });
});

// Get agency dashboard stats
router.get('/agency/stats', isAgency, (req, res) => {
    const agencyId = req.session.Id;
    
    User.findAndCountAll({
        where: {
            created_by_agency: agencyId,
            user_type: 'basic'
        }
    })
    .then(result => {
        const stats = {
            totalUsers: result.count,
            activeUsers: result.rows.filter(user => user.status == 1).length,
            inactiveUsers: result.rows.filter(user => user.status == 0).length
        };
        
        res.json({ status: true, stats: stats });
    })
    .catch(err => {
        console.error('Error fetching stats:', err);
        res.json({ status: false, message: 'Error fetching statistics!' });
    });
});

module.exports = router;
