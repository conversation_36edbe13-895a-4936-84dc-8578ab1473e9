const aiService = require("../config/ai_service");
const EmailSeries = require("../models/emailSeries");
const SeriesEmail = require("../models/seriesEmail");
const AITemplate = require("../models/aiTemplate");
const { Op } = require("sequelize");

// Get all email series for a user
module.exports.getEmailSeries = async (req, res) => {
  try {
    if (!req.session.Id) {
      return res.redirect("/");
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || "";

    const searchCondition = search
      ? {
          [Op.or]: [
            { series_name: { [Op.like]: `%${search}%` } },
            { topic: { [Op.like]: `%${search}%` } },
            { audience: { [Op.like]: `%${search}%` } },
          ],
        }
      : {};

    const result = await EmailSeries.findAndCountAll({
      where: {
        user_id: req.session.Id,
        ...searchCondition,
      },
      include: [
        {
          model: SeriesEmail,
          attributes: [
            "id",
            "email_number",
            "subject",
            "status",
            "sent_count",
            "open_count",
            "click_count",
          ],
        },
      ],
      order: [["createdAt", "DESC"]],
      limit: limit,
      offset: offset,
    });

    const totalPages = Math.ceil(result.count / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.render("ai/email-series", {
      series: result.rows,
      pagination: {
        totalPages,
        currentPage: page,
        hasNextPage,
        hasPrevPage,
      },
      search,
    });
  } catch (error) {
    console.error("Error fetching email series:", error);
    res
      .status(500)
      .json({ status: false, message: "Failed to fetch email series.", error });
  }
};

// Get AI templates
module.exports.getAITemplates = async (req, res) => {
  try {
    if (!req.session.Id) {
      return res.redirect("/");
    }

    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const search = req.query.search || "";
    const category = req.query.category || "";

    const searchCondition = search
      ? {
          [Op.or]: [
            { template_name: { [Op.like]: `%${search}%` } },
            { template_type: { [Op.like]: `%${search}%` } },
            { description: { [Op.like]: `%${search}%` } },
          ],
        }
      : {};

    const categoryCondition = category ? { category: category } : {};

    const result = await AITemplate.findAndCountAll({
      where: {
        [Op.or]: [{ user_id: req.session.Id }, { is_public: true }],
        ...searchCondition,
        ...categoryCondition,
      },
      order: [["createdAt", "DESC"]],
      limit: limit,
      offset: offset,
    });

    const totalPages = Math.ceil(result.count / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.render("ai/templates", {
      templates: result.rows,
      pagination: {
        totalPages,
        currentPage: page,
        hasNextPage,
        hasPrevPage,
      },
      search,
      category,
    });
  } catch (error) {
    console.error("Error fetching AI templates:", error);
    res
      .status(500)
      .json({ status: false, message: "Failed to fetch AI templates.", error });
  }
};

// Generate email series
module.exports.generateEmailSeries = async (req, res) => {
  try {
    const { topic, numberOfEmails, audience, tone, seriesName } = req.body;
    const userId = req.session.Id;

    if (!userId) {
      return res
        .status(401)
        .json({ status: false, message: "User not authenticated." });
    }

    const aiResponse = await aiService.generateEmailSeries(
      topic,
      numberOfEmails,
      audience,
      tone
    );

    let parsedSeries;
    try {
      parsedSeries = JSON.parse(aiResponse);
    } catch (parseError) {
      // If JSON parsing fails, try to extract content
      parsedSeries = { series: [] };
      // You might want to implement a fallback parsing method here
    }

    const newSeries = await EmailSeries.create({
      series_name: seriesName,
      topic,
      audience,
      tone,
      total_emails: numberOfEmails,
      ai_prompt: aiResponse,
      user_id: userId,
    });

    if (parsedSeries.series && parsedSeries.series.length > 0) {
      for (let email of parsedSeries.series) {
        await SeriesEmail.create({
          series_id: newSeries.id,
          email_number: email.emailNumber,
          subject: email.subject,
          content: email.content,
          cta: email.cta,
          user_id: userId,
        });
      }
    }

    res.json({
      status: true,
      message: "Email series generated successfully.",
      series: newSeries,
      redirect: "/ai/email-series",
    });
  } catch (error) {
    console.error("Error generating email series:", error);
    res
      .status(500)
      .json({
        status: false,
        message: "Failed to generate email series.",
        error: error.message,
      });
  }
};

// Generate AI template
module.exports.generateAITemplate = async (req, res) => {
  try {
    console.log("AI Controller: Received request body:", req.body);
    const { templateType, description, style, templateName, category } =
      req.body;
    const userId = req.session.Id;

    console.log("AI Controller: User ID:", userId);

    if (!userId) {
      console.log("AI Controller: User not authenticated");
      return res
        .status(401)
        .json({ status: false, message: "User not authenticated." });
    }

    if (!description || description.trim() === "") {
      console.log("AI Controller: Missing description");
      return res
        .status(400)
        .json({
          status: false,
          message: "Description is required for AI template generation.",
        });
    }

    console.log("AI Controller: Calling AI service with:", {
      templateType,
      description,
      style,
    });
    const aiResponse = await aiService.generateTemplateFromPrompt(
      templateType,
      description,
      style
    );
    console.log(
      "AI Controller: AI response received, length:",
      aiResponse.length
    );

    const newTemplate = await AITemplate.create({
      template_name: templateName || "AI Generated Template",
      template_type: templateType || "email",
      description,
      style: style || "modern",
      content: aiResponse,
      ai_prompt: `Type: ${templateType}, Description: ${description}, Style: ${style}`,
      category: category || "general",
      user_id: userId,
    });

    console.log("AI Controller: Template saved with ID:", newTemplate.id);

    res.json({
      status: true,
      message: "Email template generated successfully.",
      template: newTemplate,
      redirect: "/ai/templates",
    });
  } catch (error) {
    console.error("AI Controller Error:", {
      message: error.message,
      stack: error.stack,
      body: req.body,
    });
    res.status(500).json({
      status: false,
      message: error.message || "Failed to generate email template.",
      error: error.message,
    });
  }
};

// Generate prompt suggestions
module.exports.generatePromptSuggestions = async (req, res) => {
  try {
    const { industry, emailType, goal } = req.body;

    const suggestions = await aiService.generatePromptSuggestions(
      industry,
      emailType,
      goal
    );

    let parsedSuggestions;
    try {
      // First try to parse as JSON
      parsedSuggestions = JSON.parse(suggestions);
    } catch (parseError) {
      // If that fails, try to extract JSON from markdown code blocks
      const jsonMatch = suggestions.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonMatch) {
        try {
          parsedSuggestions = JSON.parse(jsonMatch[1]);
        } catch (innerError) {
          // If JSON parsing fails, create a simple array
          parsedSuggestions = [
            { title: "AI Generated Suggestion", description: suggestions },
          ];
        }
      } else {
        // If no JSON block found, create a simple array
        parsedSuggestions = [
          { title: "AI Generated Suggestion", description: suggestions },
        ];
      }
    }

    res.json({ status: true, suggestions: parsedSuggestions });
  } catch (error) {
    console.error("Error generating prompt suggestions:", error);
    res
      .status(500)
      .json({
        status: false,
        message: "Failed to generate prompt suggestions.",
        error: error.message,
      });
  }
};

// Get specific email series details
module.exports.getSeriesDetails = async (req, res) => {
  try {
    const seriesId = req.params.id;
    const userId = req.session.Id;

    if (!userId) {
      return res.redirect("/");
    }

    const series = await EmailSeries.findOne({
      where: { id: seriesId, user_id: userId },
      include: [
        {
          model: SeriesEmail,
          order: [["email_number", "ASC"]],
        },
      ],
    });

    if (!series) {
      return res
        .status(404)
        .json({ status: false, message: "Series not found." });
    }

    res.render("ai/series-details", { series });
  } catch (error) {
    console.error("Error fetching series details:", error);
    res
      .status(500)
      .json({
        status: false,
        message: "Failed to fetch series details.",
        error,
      });
  }
};

// Show create series form
module.exports.showCreateSeries = (req, res) => {
  if (!req.session.Id) {
    return res.redirect("/");
  }
  res.render("ai/create-series");
};

// Show create template form
module.exports.showCreateTemplate = (req, res) => {
  if (!req.session.Id) {
    return res.redirect("/");
  }
  res.render("ai/create-template");
};

// Show prompt generator
module.exports.showPromptGenerator = (req, res) => {
  if (!req.session.Id) {
    return res.redirect("/");
  }
  res.render("ai/prompt-generator");
};
