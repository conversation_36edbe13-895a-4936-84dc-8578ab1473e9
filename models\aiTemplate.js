const sequelize = require('sequelize');
const moment = require('moment');
const con = require('./connection');

// AI Template Schema
var aiTemplateSchema = con.define('ig_ai_templates', {
    template_name: {
        type: sequelize.STRING,
        allowNull: false
    },
    template_type: {
        type: sequelize.STRING,
        allowNull: false
    },
    description: {
        type: sequelize.TEXT
    },
    style: {
        type: sequelize.STRING,
        defaultValue: 'modern'
    },
    content: {
        type: sequelize.TEXT,
        allowNull: false
    },
    ai_prompt: {
        type: sequelize.TEXT
    },
    category: {
        type: sequelize.STRING,
        defaultValue: 'general'
    },
    tags: {
        type: sequelize.TEXT
    },
    usage_count: {
        type: sequelize.INTEGER,
        defaultValue: 0
    },
    is_public: {
        type: sequelize.BOOLEAN,
        defaultValue: false
    },
    user_id: {
        type: sequelize.INTEGER,
        allowNull: false
    },
    createdAt: {
        type: sequelize.DATE,
        get: function () {
            const rawValue = this.getDataValue('createdAt');
            if (rawValue) {
                const isoDate = moment(rawValue, moment.ISO_8601);
                return isoDate.isValid() ? isoDate.format('YYYY-MM-DD HH:mm:ss') : false;
            }
            return false;
        }
    },
    updatedAt: {
        type: sequelize.DATE,
        get: function () {
            const rawValue = this.getDataValue('updatedAt');
            if (rawValue) {
                const isoDate = moment(rawValue, moment.ISO_8601);
                return isoDate.isValid() ? isoDate.format('YYYY-MM-DD HH:mm:ss') : false;
            }
            return false;
        }
    }
});

// Remove immediate sync - will be handled centrally

module.exports = aiTemplateSchema;
