const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');

// Models
const User = require('../models/users');

// Middleware to ensure the user is an admin
function isAdmin(req, res, next) {
    if (req.session.userType === 'admin') {
        return next();
    }
    res.redirect('/');
}

// Admin dashboard
router.get('/admin/users', isAdmin, (req, res) => {
    User.findAll({ order: [['id', 'DESC']] })
        .then(users => {
            res.render('admin/users', { users, userType: req.session.userType });
        })
        .catch(err => {
            res.status(500).json({ status: false, message: 'Could not load users.' });
        });
});

// Create new user
router.post('/admin/users/create', isAdmin, (req, res) => {
    const { email, full_name, password, user_type } = req.body;
    
    User.findOne({ where: { email: email } })
        .then(existingUser => {
            if (existingUser) {
                return res.json({ status: false, message: 'Email already exists!' });
            }
            
            bcrypt.hash(password, 8, (err, hash) => {
                const newUser = {
                    email: email,
                    full_name: full_name,
                    password: hash,
                    user_type: user_type
                };
                
                User.create(newUser)
                    .then(user => {
                        res.json({ status: true, message: 'User created successfully!' });
                    })
                    .catch(err => {
                        res.json({ status: false, message: 'Error creating user!' });
                    });
            });
        })
        .catch(err => {
            res.json({ status: false, message: 'Error checking email!' });
        });
});

// Get single user
router.get('/admin/users/get/:id', isAdmin, (req, res) => {
    const userId = req.params.id;
    
    User.findOne({ where: { id: userId } })
        .then(user => {
            if (user) {
                res.json({ status: true, user: user });
            } else {
                res.json({ status: false, message: 'User not found!' });
            }
        })
        .catch(err => {
            res.json({ status: false, message: 'Error fetching user!' });
        });
});

// Update user
router.put('/admin/users/update/:id', isAdmin, (req, res) => {
    const userId = req.params.id;
    const { email, full_name, password, user_type, status } = req.body;
    
    // First check if email is being changed and if it's already taken
    User.findOne({ where: { id: userId } })
        .then(user => {
            if (!user) {
                return res.json({ status: false, message: 'User not found!' });
            }
            
            // Check if email is being changed
            if (email !== user.email) {
                User.findOne({ where: { email: email } })
                    .then(existingUser => {
                        if (existingUser) {
                            return res.json({ status: false, message: 'Email already exists!' });
                        }
                        
                        // Proceed with update
                        updateUserData();
                    })
                    .catch(err => {
                        res.json({ status: false, message: 'Error checking email!' });
                    });
            } else {
                // Email not changed, proceed with update
                updateUserData();
            }
            
            function updateUserData() {
                const updateData = {
                    email: email,
                    full_name: full_name,
                    user_type: user_type,
                    status: status
                };
                
                // Only update password if provided
                if (password && password.trim() !== '') {
                    bcrypt.hash(password, 8, (err, hash) => {
                        if (err) {
                            return res.json({ status: false, message: 'Error hashing password!' });
                        }
                        
                        updateData.password = hash;
                        updateData.resetPasswordToken = hash;
                        
                        performUpdate();
                    });
                } else {
                    performUpdate();
                }
                
                function performUpdate() {
                    User.update(updateData, { where: { id: userId } })
                        .then(result => {
                            res.json({ status: true, message: 'User updated successfully!' });
                        })
                        .catch(err => {
                            res.json({ status: false, message: 'Error updating user!' });
                        });
                }
            }
        })
        .catch(err => {
            res.json({ status: false, message: 'Error finding user!' });
        });
});

// Delete user
router.delete('/admin/users/delete/:id', isAdmin, (req, res) => {
    const userId = req.params.id;
    
    // Prevent admin from deleting themselves
    if (userId == req.session.Id) {
        return res.json({ status: false, message: 'You cannot delete your own account!' });
    }
    
    User.destroy({ where: { id: userId } })
        .then(result => {
            if (result) {
                res.json({ status: true, message: 'User deleted successfully!' });
            } else {
                res.json({ status: false, message: 'User not found!' });
            }
        })
        .catch(err => {
            res.json({ status: false, message: 'Error deleting user!' });
        });
});

module.exports = router;

