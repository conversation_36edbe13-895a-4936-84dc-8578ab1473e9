<%- include('../header', {menu:'ai'}) %>

<div class="ig_content_wrapper">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="ig_page_header">
                    <h2>AI Prompt Generator</h2>
                    <p>Generate creative email prompts for your campaigns</p>
                </div>

                <div class="ig_form_wrapper">
                    <form id="promptGeneratorForm" method="POST" action="/ai/generate-prompts">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="ig_input_wrapper">
                                    <label for="industry">Industry</label>
                                    <select id="industry" name="industry" class="ig_input ig_select" required>
                                        <option value="">Select Industry</option>
                                        <option value="technology">Technology</option>
                                        <option value="healthcare">Healthcare</option>
                                        <option value="finance">Finance</option>
                                        <option value="education">Education</option>
                                        <option value="retail">Retail</option>
                                        <option value="real-estate">Real Estate</option>
                                        <option value="food-beverage">Food & Beverage</option>
                                        <option value="travel">Travel</option>
                                        <option value="automotive">Automotive</option>
                                        <option value="fashion">Fashion</option>
                                        <option value="fitness">Fitness</option>
                                        <option value="beauty">Beauty</option>
                                        <option value="consulting">Consulting</option>
                                        <option value="non-profit">Non-Profit</option>
                                        <option value="other">Other</option>
                                    </select>
                                    <span class="error-message" id="industryError"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="ig_input_wrapper">
                                    <label for="emailType">Email Type</label>
                                    <select id="emailType" name="emailType" class="ig_input ig_select" required>
                                        <option value="">Select Email Type</option>
                                        <option value="newsletter">Newsletter</option>
                                        <option value="promotional">Promotional</option>
                                        <option value="welcome">Welcome Series</option>
                                        <option value="follow-up">Follow-up</option>
                                        <option value="abandoned-cart">Abandoned Cart</option>
                                        <option value="product-launch">Product Launch</option>
                                        <option value="event-invitation">Event Invitation</option>
                                        <option value="survey">Survey</option>
                                        <option value="thank-you">Thank You</option>
                                        <option value="re-engagement">Re-engagement</option>
                                        <option value="sales">Sales</option>
                                        <option value="announcement">Announcement</option>
                                    </select>
                                    <span class="error-message" id="emailTypeError"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="ig_input_wrapper">
                                    <label for="goal">Campaign Goal</label>
                                    <select id="goal" name="goal" class="ig_input ig_select" required>
                                        <option value="">Select Campaign Goal</option>
                                        <option value="increase-sales">Increase Sales</option>
                                        <option value="build-awareness">Build Brand Awareness</option>
                                        <option value="generate-leads">Generate Leads</option>
                                        <option value="drive-traffic">Drive Website Traffic</option>
                                        <option value="customer-retention">Customer Retention</option>
                                        <option value="engagement">Increase Engagement</option>
                                        <option value="education">Educate Audience</option>
                                        <option value="feedback">Collect Feedback</option>
                                        <option value="event-promotion">Event Promotion</option>
                                        <option value="newsletter-signup">Newsletter Signup</option>
                                        <option value="product-demo">Product Demo</option>
                                        <option value="customer-support">Customer Support</option>
                                    </select>
                                    <span class="error-message" id="goalError"></span>
                                </div>
                            </div>
                        </div>

                        <div class="ig_form_actions">
                            <button type="submit" class="ig_btn ig_btn_primary" id="generatePromptsBtn">
                                <i class="fas fa-magic"></i> Generate Prompts
                            </button>
                            <a href="/ai/templates" class="ig_btn ig_btn_secondary">
                                <i class="fas fa-arrow-left"></i> Back to AI Features
                            </a>
                        </div>
                    </form>
                </div>

                <!-- AI Generation Status -->
                <div id="generationStatus" class="ig_status_wrapper" style="display: none;">
                    <div class="ig_loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>AI is generating prompt suggestions...</span>
                    </div>
                </div>

                <!-- Generated Prompts -->
                <div id="promptResults" class="ig_prompt_results" style="display: none;">
                    <h3>Generated Prompt Suggestions</h3>
                    <div id="promptList" class="ig_prompt_list">
                        <!-- Prompts will be displayed here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#promptGeneratorForm').on('submit', function(e) {
        e.preventDefault();
        
        // Clear previous errors
        $('.error-message').text('');
        
        // Show loading spinner
        $('#generationStatus').html('<div class="ig_loading"><i class="fas fa-spinner fa-spin"></i> Generating prompts...</div>').show();
        $('#promptResults').hide();
        $('#generatePromptsBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Generating...');
        
        // Get form data
        const formData = {
            industry: $('#industry').val(),
            emailType: $('#emailType').val(),
            goal: $('#goal').val()
        };
        
        // Validate required fields
        let isValid = true;
        const requiredFields = ['industry', 'emailType', 'goal'];
        
        requiredFields.forEach(field => {
            if (!formData[field]) {
                $(`#${field}Error`).text('This field is required');
                isValid = false;
            }
        });
        
        if (!isValid) {
            $('#generationStatus').hide();
            $('#generatePromptsBtn').prop('disabled', false).html('<i class="fas fa-magic"></i> Generate Prompts');
            return;
        }
        
        // Submit to AI
        $.ajax({
            url: '/ai/generate-prompts',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.status) {
                    displayPrompts(response.suggestions);
                    $('#promptResults').show();
                    
                    // Scroll to results
                    $('html, body').animate({
                        scrollTop: $('#promptResults').offset().top
                    }, 1000);
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                alert('Error generating prompts: ' + (response ? response.message : 'Unknown error'));
            },
            complete: function() {
                $('#generationStatus').hide();
                $('#generatePromptsBtn').prop('disabled', false).html('<i class="fas fa-magic"></i> Generate Prompts');
            }
        });
    });

    function displayPrompts(suggestions) {
        const promptList = $('#promptList');
        promptList.empty();
        
        if (Array.isArray(suggestions) && suggestions.length > 0) {
            suggestions.forEach((suggestion, index) => {
                const promptContent = suggestion.description || suggestion;
                const promptCard = $(`
                    <div class="ig_prompt_card">
                        <div class="ig_prompt_header">
                            <h4><i class="fas fa-magic"></i> ${suggestion.title || 'Prompt ' + (index + 1)}</h4>
                        </div>
                        <div class="ig_prompt_chat_bubble">
                            <div class="ig_chat_message">
                                <div class="ig_chat_avatar">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="ig_chat_content">
                                    <p>${formatPromptText(promptContent)}</p>
                                </div>
                            </div>
                        </div>
                        <div class="ig_prompt_actions">
                            <button class="ig_btn ig_btn_small ig_btn_primary copy-prompt" data-prompt="${promptContent}">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                            <button class="ig_btn ig_btn_small ig_btn_secondary use-prompt" data-prompt="${promptContent}">
                                <i class="fas fa-arrow-right"></i> Use This
                            </button>
                        </div>
                    </div>
                `);
                promptList.append(promptCard);
            });
        } else {
            promptList.html('<div class="ig_no_prompts"><i class="fas fa-info-circle"></i> No prompts generated. Please try again.</div>');
        }
    }
    
    function formatPromptText(text) {
        // Basic text formatting for better display
        return text.replace(/\n/g, '<br>')
                  .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                  .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    // Copy prompt to clipboard
    $(document).on('click', '.copy-prompt', function() {
        const prompt = $(this).data('prompt');
        navigator.clipboard.writeText(prompt).then(function() {
            alert('Prompt copied to clipboard!');
        });
    });

    // Use prompt for new template
    $(document).on('click', '.use-prompt', function() {
        const prompt = $(this).data('prompt');
        const encodedPrompt = encodeURIComponent(prompt);
        window.location.href = `/ai/create-template?prompt=${encodedPrompt}`;
    });
});
</script>

<style>
.ig_prompt_results {
    background: white;
    border-radius: 6px;
    padding: 30px;
    margin-top: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ig_prompt_results h3 {
    margin: 0 0 20px 0;
    color: var(--color-heading);
}

.ig_prompt_list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.ig_prompt_card {
    background: #f8f9fa;
    border-radius: 6px;
    padding: 20px;
    border: 1px solid #e0e0e0;
    transition: transform 0.3s ease;
}

.ig_prompt_card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.ig_prompt_header h4 {
    margin: 0 0 10px 0;
    color: var(--color-heading);
}

.ig_prompt_body p {
    margin: 0 0 15px 0;
    color: var(--color-body);
    line-height: 1.6;
}

.ig_prompt_actions {
    display: flex;
    gap: 10px;
}

.ig_btn_small {
    padding: 8px 12px;
    font-size: 12px;
    min-width: auto;
}
</style>

<%- include('../footer') %>
