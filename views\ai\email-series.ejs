<%- include('../header', {menu:'ai'}) %>

<div class="ig_content_wrapper">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="ig_page_header">
                    <h2>AI Email Series</h2>
                    <p>Manage your AI-generated email series campaigns</p>
                    <div class="ig_page_actions">
                        <a href="/ai/create-series" class="ig_btn ig_btn_primary">
                            <i class="fas fa-plus"></i> Create New Series
                        </a>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="ig_filter_wrapper">
                    <form method="GET" action="/ai/email-series">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="ig_input_wrapper">
                                    <input type="text" name="search" class="ig_input" placeholder="Search by series name, topic, or audience..." value="<%= search %>">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <button type="submit" class="ig_btn ig_btn_primary">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <% if(search) { %>
                                    <a href="/ai/email-series" class="ig_btn ig_btn_secondary">Clear</a>
                                <% } %>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Series List -->
                <div class="ig_table_wrapper">
                    <% if(series && series.length > 0) { %>
                        <table class="ig_table">
                            <thead>
                                <tr>
                                    <th>Series Name</th>
                                    <th>Topic</th>
                                    <th>Audience</th>
                                    <th>Total Emails</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <% series.forEach(function(item) { %>
                                    <tr>
                                        <td><%= item.series_name %></td>
                                        <td><%= item.topic %></td>
                                        <td><%= item.audience %></td>
                                        <td><%= item.total_emails %></td>
                                        <td>
                                            <span class="ig_status ig_status_<%= item.status %>">
                                                <%= item.status.charAt(0).toUpperCase() + item.status.slice(1) %>
                                            </span>
                                        </td>
                                        <td><%= item.createdAt %></td>
                                        <td>
                                            <div class="ig_action_buttons">
                                                <a href="/ai/series/<%= item.id %>" class="ig_btn ig_btn_small ig_btn_primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <a href="/ai/series/<%= item.id %>/edit" class="ig_btn ig_btn_small ig_btn_secondary">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                                <button class="ig_btn ig_btn_small ig_btn_danger delete-series" data-id="<%= item.id %>">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>

                        <!-- Pagination -->
                        <% if(pagination.totalPages > 1) { %>
                            <div class="ig_pagination">
                                <% if(pagination.hasPrevPage) { %>
                                    <a href="/ai/email-series?page=<%= pagination.currentPage - 1 %>&search=<%= search %>" class="ig_btn ig_btn_secondary">
                                        <i class="fas fa-chevron-left"></i> Previous
                                    </a>
                                <% } %>
                                
                                <span class="ig_page_info">
                                    Page <%= pagination.currentPage %> of <%= pagination.totalPages %>
                                </span>
                                
                                <% if(pagination.hasNextPage) { %>
                                    <a href="/ai/email-series?page=<%= pagination.currentPage + 1 %>&search=<%= search %>" class="ig_btn ig_btn_secondary">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </a>
                                <% } %>
                            </div>
                        <% } %>
                    <% } else { %>
                        <div class="ig_empty_state">
                            <div class="ig_empty_icon">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h3>No Email Series Found</h3>
                            <p>Create your first AI-powered email series to get started.</p>
                            <a href="/ai/create-series" class="ig_btn ig_btn_primary">
                                <i class="fas fa-plus"></i> Create Email Series
                            </a>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('.delete-series').click(function() {
        const seriesId = $(this).data('id');
        if(confirm('Are you sure you want to delete this email series?')) {
            // Delete series logic
            $.ajax({
                url: `/ai/series/${seriesId}`,
                method: 'DELETE',
                success: function(response) {
                    if(response.status) {
                        location.reload();
                    } else {
                        alert('Error deleting series: ' + response.message);
                    }
                },
                error: function() {
                    alert('Error deleting series');
                }
            });
        }
    });
});
</script>

<%- include('../footer') %>
