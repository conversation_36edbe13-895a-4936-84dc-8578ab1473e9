const Together = require("together-ai");
require("dotenv").config();

class AIService {
  constructor() {
    this.together = new Together({
      apiKey: process.env.TOGETHER_API_KEY,
    });
  }

  async generateEmailContent(prompt, streamCallback = null) {
    try {
      console.log(
        "AI Service: Generating content with prompt:",
        prompt.substring(0, 100) + "..."
      );

      if (!this.together) {
        throw new Error("Together AI client not initialized");
      }

      if (!process.env.TOGETHER_API_KEY) {
        throw new Error("TOGETHER_API_KEY not found in environment variables");
      }

      const response = await this.together.chat.completions.create({
        messages: [
          {
            role: "system",
            content:
              "You are an expert email marketing copywriter. Create engaging, professional email content based on the user's requirements.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        model: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
        stream: !!streamCallback,
        max_tokens: 2000,
        temperature: 0.7,
      });

      if (streamCallback) {
        let fullContent = "";
        for await (const token of response) {
          const content = token.choices[0]?.delta?.content || "";
          fullContent += content;
          streamCallback(content);
        }
        console.log(
          "AI Service: Generated content length:",
          fullContent.length
        );
        return fullContent;
      } else {
        const content = response.choices[0]?.message?.content || "";
        console.log("AI Service: Generated content length:", content.length);
        return content;
      }
    } catch (error) {
      console.error("AI Service Error Details:", {
        message: error.message,
        stack: error.stack,
        response: error.response?.data,
      });

      if (error.message.includes("API key")) {
        throw new Error(
          "Invalid API key. Please check your Together AI configuration."
        );
      } else if (error.message.includes("rate limit")) {
        throw new Error("Rate limit exceeded. Please try again later.");
      } else if (
        error.message.includes("network") ||
        error.code === "ECONNREFUSED"
      ) {
        throw new Error(
          "Network error. Please check your internet connection."
        );
      } else {
        throw new Error(`Failed to generate content: ${error.message}`);
      }
    }
  }

  async generateEmailSeries(
    topic,
    numberOfEmails,
    audience,
    tone = "professional"
  ) {
    const prompt = `Create a ${numberOfEmails}-part email series about "${topic}" for ${audience}. 
        Use a ${tone} tone. For each email, provide:
        1. Subject line
        2. Email content (HTML format)
        3. Call-to-action suggestion
        
        Make each email valuable and progressive, building upon the previous one.
        Format the response as JSON with the following structure:
        {
            "series": [
                {
                    "emailNumber": 1,
                    "subject": "Email subject here",
                    "content": "HTML email content here",
                    "cta": "Call to action text"
                }
            ]
        }`;

    return await this.generateEmailContent(prompt);
  }

  async generateTemplateFromPrompt(
    templateType,
    description,
    style = "modern"
  ) {
    const prompt = `Create a professional ${templateType} email template based on this description: "${description}"

        Style: ${style}

        IMPORTANT: Generate ONLY the HTML table structure that fits within an email campaign system. The template should:

        1. Use this exact main table structure:
        <table class="ig_main_table" style="width: 100%;padding: 50px 10px;">
            <tbody data-element="body" style="background-color: #ffffff;">
                <tr>
                    <td style="padding: 0;">
                        <table class="ig_inner_table" data-element="template" style="background-color: #fbfbfb; width: 600px; margin: 30px auto;">
                            <tbody class="el_target_wrapper">
                                <!-- Your content rows go here -->
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>

        2. Inside the el_target_wrapper tbody, create rows with these data attributes:
        - data-element="header" for header sections
        - data-element="text" for text content
        - data-element="image" for images
        - data-element="button" for buttons
        - data-element="footer" for footer

        3. Use inline CSS styling for all elements
        4. Make it mobile-responsive with max-width: 600px
        5. Use professional colors and typography
        6. Include proper spacing and padding
        7. Make the content relevant to the description provided

        Generate a complete, ready-to-use email template that matches this exact structure and includes relevant content based on the description.`;

    return await this.generateEmailContent(prompt);
  }

  async generatePromptSuggestions(industry, emailType, goal) {
    const prompt = `Generate 5 creative email prompt suggestions for:
        Industry: ${industry}
        Email Type: ${emailType}
        Goal: ${goal}
        
        Each prompt should be detailed and actionable for creating effective email campaigns.
        Return ONLY a valid JSON array with this exact structure:
        [
            {
                "title": "Brief descriptive title",
                "description": "Detailed actionable description for the email campaign"
            }
        ]
        
        Do not include any markdown formatting, code blocks, or additional text. Just return the JSON array.`;

    return await this.generateEmailContent(prompt);
  }

  async improveEmailContent(existingContent, improvements) {
    const prompt = `Improve the following email content based on these requirements:
        Improvements needed: ${improvements}
        
        Original content:
        ${existingContent}
        
        Provide the improved version with better engagement, clarity, and conversion potential.`;

    return await this.generateEmailContent(prompt);
  }
}

module.exports = new AIService();
