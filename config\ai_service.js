const Together = require("together-ai");
require('dotenv').config();

class AIService {
    constructor() {
        this.together = new Together({
            apiKey: process.env.TOGETHER_API_KEY
        });
    }

    async generateEmailContent(prompt, streamCallback = null) {
        try {
            const response = await this.together.chat.completions.create({
                messages: [
                    {
                        role: "system",
                        content: "You are an expert email marketing copywriter. Create engaging, professional email content based on the user's requirements."
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                model: "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
                stream: !!streamCallback
            });

            if (streamCallback) {
                let fullContent = '';
                for await (const token of response) {
                    const content = token.choices[0]?.delta?.content || '';
                    fullContent += content;
                    streamCallback(content);
                }
                return fullContent;
            } else {
                return response.choices[0]?.message?.content || '';
            }
        } catch (error) {
            console.error('AI Service Error:', error);
            throw new Error('Failed to generate content');
        }
    }

    async generateEmailSeries(topic, numberOfEmails, audience, tone = 'professional') {
        const prompt = `Create a ${numberOfEmails}-part email series about "${topic}" for ${audience}. 
        Use a ${tone} tone. For each email, provide:
        1. Subject line
        2. Email content (HTML format)
        3. Call-to-action suggestion
        
        Make each email valuable and progressive, building upon the previous one.
        Format the response as JSON with the following structure:
        {
            "series": [
                {
                    "emailNumber": 1,
                    "subject": "Email subject here",
                    "content": "HTML email content here",
                    "cta": "Call to action text"
                }
            ]
        }`;

        return await this.generateEmailContent(prompt);
    }

    async generateTemplateFromPrompt(templateType, description, style = 'modern') {
        const prompt = `Create a ${templateType} email template with the following requirements:
        Description: ${description}
        Style: ${style}
        
        Generate clean, responsive HTML email template with inline CSS.
        Include placeholders for:
        - {{company_name}}
        - {{recipient_name}}
        - {{content}}
        - {{cta_button}}
        - {{footer}}
        
        Make it mobile-responsive and compatible with major email clients.`;

        return await this.generateEmailContent(prompt);
    }

    async generatePromptSuggestions(industry, emailType, goal) {
        const prompt = `Generate 5 creative email prompt suggestions for:
        Industry: ${industry}
        Email Type: ${emailType}
        Goal: ${goal}
        
        Each prompt should be detailed and actionable for creating effective email campaigns.
        Return ONLY a valid JSON array with this exact structure:
        [
            {
                "title": "Brief descriptive title",
                "description": "Detailed actionable description for the email campaign"
            }
        ]
        
        Do not include any markdown formatting, code blocks, or additional text. Just return the JSON array.`;

        return await this.generateEmailContent(prompt);
    }

    async improveEmailContent(existingContent, improvements) {
        const prompt = `Improve the following email content based on these requirements:
        Improvements needed: ${improvements}
        
        Original content:
        ${existingContent}
        
        Provide the improved version with better engagement, clarity, and conversion potential.`;

        return await this.generateEmailContent(prompt);
    }
}

module.exports = new AIService();
