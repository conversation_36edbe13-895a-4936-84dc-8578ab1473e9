<%- include('../header', {menu:'ai'}) %>

<div class="ig_content_wrapper">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="ig_page_header">
                    <h2>Create AI Template</h2>
                    <p>Generate a custom email template using AI</p>
                </div>

                <div class="ig_form_wrapper">
                    <form id="createTemplateForm" method="POST" action="/ai/generate-template">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="ig_input_wrapper">
                                    <label for="templateName">Template Name</label>
                                    <input type="text" id="templateName" name="templateName" class="ig_input" required placeholder="Enter template name">
                                    <span class="error-message" id="templateNameError"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="ig_input_wrapper">
                                    <label for="templateType">Template Type</label>
                                    <select id="templateType" name="templateType" class="ig_input ig_select" required>
                                        <option value="">Select template type</option>
                                        <option value="newsletter">Newsletter</option>
                                        <option value="promotional">Promotional</option>
                                        <option value="welcome">Welcome Email</option>
                                        <option value="follow-up">Follow-up</option>
                                        <option value="announcement">Announcement</option>
                                        <option value="event-invitation">Event Invitation</option>
                                        <option value="thank-you">Thank You</option>
                                        <option value="sales">Sales</option>
                                    </select>
                                    <span class="error-message" id="templateTypeError"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="ig_input_wrapper">
                                    <label for="description">Description</label>
                                    <textarea id="description" name="description" class="ig_input" rows="4" required placeholder="Describe what you want in this template..."></textarea>
                                    <span class="error-message" id="descriptionError"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="ig_input_wrapper">
                                    <label for="style">Style</label>
                                    <select id="style" name="style" class="ig_input ig_select" required>
                                        <option value="modern">Modern</option>
                                        <option value="classic">Classic</option>
                                        <option value="minimal">Minimal</option>
                                        <option value="corporate">Corporate</option>
                                        <option value="creative">Creative</option>
                                        <option value="elegant">Elegant</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="ig_input_wrapper">
                                    <label for="category">Category</label>
                                    <select id="category" name="category" class="ig_input ig_select" required>
                                        <option value="general">General</option>
                                        <option value="newsletter">Newsletter</option>
                                        <option value="promotional">Promotional</option>
                                        <option value="welcome">Welcome</option>
                                        <option value="follow-up">Follow-up</option>
                                        <option value="transactional">Transactional</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Options -->
                        <div class="ig_advanced_options">
                            <h4>Advanced Options</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="ig_input_wrapper">
                                        <label for="targetAudience">Target Audience</label>
                                        <input type="text" id="targetAudience" name="targetAudience" class="ig_input" placeholder="e.g., small business owners, tech professionals">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="ig_input_wrapper">
                                        <label for="industry">Industry</label>
                                        <input type="text" id="industry" name="industry" class="ig_input" placeholder="e.g., technology, healthcare, education">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="ig_input_wrapper">
                                        <label for="tone">Tone</label>
                                        <select id="tone" name="tone" class="ig_input ig_select">
                                            <option value="professional">Professional</option>
                                            <option value="friendly">Friendly</option>
                                            <option value="casual">Casual</option>
                                            <option value="formal">Formal</option>
                                            <option value="enthusiastic">Enthusiastic</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="ig_input_wrapper">
                                        <label for="primaryColor">Primary Color</label>
                                        <input type="color" id="primaryColor" name="primaryColor" class="ig_input" value="#4a148c">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="ig_form_actions">
                            <button type="submit" class="ig_btn ig_btn_primary" id="generateTemplateBtn">
                                <i class="fas fa-robot"></i> Generate Template
                            </button>
                            <a href="/ai/templates" class="ig_btn ig_btn_secondary">
                                <i class="fas fa-arrow-left"></i> Back to Templates
                            </a>
                        </div>
                    </form>
                </div>

                <!-- AI Generation Status -->
                <div id="generationStatus" class="ig_status_wrapper" style="display: none;">
                    <div class="ig_loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>AI is generating your template...</span>
                    </div>
                </div>

                <!-- Template Preview -->
                <div id="templatePreview" class="ig_template_preview" style="display: none;">
                    <h3>Generated Template Preview</h3>
                    <div id="previewContent"></div>
                    <div class="ig_preview_actions">
                        <button class="ig_btn ig_btn_primary" id="saveTemplateBtn">
                            <i class="fas fa-save"></i> Save Template
                        </button>
                        <button class="ig_btn ig_btn_secondary" id="regenerateBtn">
                            <i class="fas fa-redo"></i> Regenerate
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    let generatedTemplateData = null;

    $('#createTemplateForm').on('submit', function(e) {
        e.preventDefault();
        
        // Clear previous errors
        $('.error-message').text('');
        
        // Show loading
        $('#generationStatus').show();
        $('#generateTemplateBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Generating...');
        
        // Get form data
        const formData = {
            templateName: $('#templateName').val(),
            templateType: $('#templateType').val(),
            description: $('#description').val(),
            style: $('#style').val(),
            category: $('#category').val(),
            targetAudience: $('#targetAudience').val(),
            industry: $('#industry').val(),
            tone: $('#tone').val(),
            primaryColor: $('#primaryColor').val()
        };
        
        // Validate required fields
        let isValid = true;
        const requiredFields = ['templateName', 'templateType', 'description', 'style', 'category'];
        
        requiredFields.forEach(field => {
            if (!formData[field]) {
                $(`#${field}Error`).text('This field is required');
                isValid = false;
            }
        });
        
        if (!isValid) {
            $('#generationStatus').hide();
            $('#generateTemplateBtn').prop('disabled', false).html('<i class="fas fa-robot"></i> Generate Template');
            return;
        }
        
        // Submit to AI
        $.ajax({
            url: '/ai/generate-template',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.status) {
                    // Store the generated template data
                    generatedTemplateData = response.template;
                    
                    // Show preview
                    $('#previewContent').html(response.template.content);
                    $('#templatePreview').show();
                    
                    // Scroll to preview
                    $('html, body').animate({
                        scrollTop: $('#templatePreview').offset().top
                    }, 1000);
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                alert('Error generating template: ' + (response ? response.message : 'Unknown error'));
            },
            complete: function() {
                $('#generationStatus').hide();
                $('#generateTemplateBtn').prop('disabled', false).html('<i class="fas fa-robot"></i> Generate Template');
            }
        });
    });

    // Save template
    $('#saveTemplateBtn').click(function() {
        if (generatedTemplateData) {
            alert('Template saved successfully!');
            window.location.href = '/ai/templates';
        }
    });

    // Regenerate template
    $('#regenerateBtn').click(function() {
        $('#templatePreview').hide();
        $('#createTemplateForm').trigger('submit');
    });
});
</script>

<%- include('../footer') %>
