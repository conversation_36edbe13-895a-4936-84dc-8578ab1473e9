var createError = require('http-errors');
var express = require('express');
var path = require('path');
var cookieParser = require('cookie-parser');
var logger = require('morgan');
var session = require('express-session');
require('dotenv').config();

// console.log("cred here", process.env.BASE_URL)
//routes require
var usersRouter = require('./routes/users');
var dashboardRouter = require('./routes/dashboard');
var campaignRouter = require('./routes/campaigns');
var contactRouter = require('./routes/contact');
var settingRouter = require('./routes/settings');
var aiRouter = require('./routes/ai');
var adminRouter = require('./routes/admin');
var agencyRouter = require('./routes/agency');

// Middleware
var userTypeMiddleware = require('./middleware/userType');

var app = express();

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');

app.use(logger('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));
app.use(session({secret : 'inboxGun#21@',resave: false,saveUninitialized: true}));

// Apply userType middleware after session
app.use(userTypeMiddleware);

app.use('/', usersRouter);
app.use('/dashboard', dashboardRouter);
app.use('/campaigns', campaignRouter);
app.use('/contacts', contactRouter);
app.use('/settings', settingRouter);
app.use('/ai', aiRouter);
app.use('/', adminRouter);
app.use('/', agencyRouter);

// catch 404 and forward to error handler
app.use(function(req, res, next) {
  next(createError(404));
});

// error handler
app.use(function(err, req, res, next) {
  // set locals, only providing error in development
  res.locals.message = err.message;
  res.locals.error = req.app.get('env') === 'development' ? err : {};

  // render the error page
  res.status(err.status || 500);
  res.render('error');
});

module.exports = app;
