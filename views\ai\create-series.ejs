<%- include('../header', {menu:'ai'}) %>

<div class="ig_content_wrapper">
    <div class="container">
        <div class="row">
            <div class="col-md-12">
                <div class="ig_page_header">
                    <h2>Create AI Email Series</h2>
                    <p>Generate a comprehensive email series using AI</p>
                </div>

                <div class="ig_form_wrapper">
                    <form id="createSeriesForm" method="POST" action="/ai/generate-series">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="ig_input_wrapper">
                                    <label for="seriesName">Series Name</label>
                                    <input type="text" id="seriesName" name="seriesName" class="ig_input" required placeholder="Enter series name">
                                    <span class="error-message" id="seriesNameError"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="ig_input_wrapper">
                                    <label for="topic">Topic</label>
                                    <input type="text" id="topic" name="topic" class="ig_input" required placeholder="What's the main topic?">
                                    <span class="error-message" id="topicError"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="ig_input_wrapper">
                                    <label for="audience">Target Audience</label>
                                    <input type="text" id="audience" name="audience" class="ig_input" required placeholder="Who is your audience?">
                                    <span class="error-message" id="audienceError"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="ig_input_wrapper">
                                    <label for="numberOfEmails">Number of Emails</label>
                                    <select id="numberOfEmails" name="numberOfEmails" class="ig_input ig_select" required>
                                        <option value="">Select number of emails</option>
                                        <option value="3">3 Emails</option>
                                        <option value="5">5 Emails</option>
                                        <option value="7">7 Emails</option>
                                        <option value="10">10 Emails</option>
                                    </select>
                                    <span class="error-message" id="numberOfEmailsError"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="ig_input_wrapper">
                                    <label for="tone">Tone</label>
                                    <select id="tone" name="tone" class="ig_input ig_select" required>
                                        <option value="professional">Professional</option>
                                        <option value="friendly">Friendly</option>
                                        <option value="casual">Casual</option>
                                        <option value="formal">Formal</option>
                                        <option value="enthusiastic">Enthusiastic</option>
                                        <option value="conversational">Conversational</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="ig_form_actions">
                            <button type="submit" class="ig_btn ig_btn_primary" id="generateBtn">
                                <i class="fas fa-robot"></i> Generate Series
                            </button>
                            <a href="/ai/email-series" class="ig_btn ig_btn_secondary">
                                <i class="fas fa-arrow-left"></i> Back to Series
                            </a>
                        </div>
                    </form>
                </div>

                <!-- AI Generation Status -->
                <div id="generationStatus" class="ig_status_wrapper" style="display: none;">
                    <div class="ig_loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>AI is generating your email series...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#createSeriesForm').on('submit', function(e) {
        e.preventDefault();
        
        // Clear previous errors
        $('.error-message').text('');
        
        // Show loading
        $('#generationStatus').show();
        $('#generateBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Generating...');
        
        // Get form data
        const formData = {
            seriesName: $('#seriesName').val(),
            topic: $('#topic').val(),
            audience: $('#audience').val(),
            numberOfEmails: $('#numberOfEmails').val(),
            tone: $('#tone').val()
        };
        
        // Validate form
        let isValid = true;
        Object.keys(formData).forEach(key => {
            if (!formData[key]) {
                $(`#${key}Error`).text('This field is required');
                isValid = false;
            }
        });
        
        if (!isValid) {
            $('#generationStatus').hide();
            $('#generateBtn').prop('disabled', false).html('<i class="fas fa-robot"></i> Generate Series');
            return;
        }
        
        // Submit to AI
        $.ajax({
            url: '/ai/generate-series',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.status) {
                    // Show success message
                    alert('Email series generated successfully!');
                    // Redirect to series list
                    window.location.href = '/ai/email-series';
                } else {
                    alert('Error: ' + response.message);
                }
            },
            error: function(xhr) {
                const response = xhr.responseJSON;
                alert('Error generating series: ' + (response ? response.message : 'Unknown error'));
            },
            complete: function() {
                $('#generationStatus').hide();
                $('#generateBtn').prop('disabled', false).html('<i class="fas fa-robot"></i> Generate Series');
            }
        });
    });
});
</script>

<%- include('../footer') %>
